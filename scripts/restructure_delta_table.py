# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Delta Lake Table Restructuring Script

Restructures an existing Delta table to use date-level partitions and 256MB parquet files.
Uses a hybrid approach: DuckDB for efficient data processing + Delta Lake for writing.

Key Features:
- Converts year/month partitions to date-level partitions
- Optimizes for 256MB minimum parquet file size
- Memory-efficient chunked processing (handles large tables)
- Copies to new S3 location (preserves original table)
- Dry-run mode for safe testing
- Resume capability for interrupted operations
- Progress tracking and validation

Usage:
    # Dry run first (recommended)
    uv run scripts/restructure_delta_table.py \
        s3://bucket/source-table \
        s3://bucket/restructured-table \
        --dry-run

    # Full restructure with date partitioning
    uv run scripts/restructure_delta_table.py \
        s3://bucket/source-table \
        s3://bucket/restructured-table \
        --date-column datetime \
        --target-file-size 268435456

    # Resume interrupted operation
    uv run scripts/restructure_delta_table.py \
        s3://bucket/source-table \
        s3://bucket/restructured-table \
        --resume

Example:
    uv run scripts/restructure_delta_table.py \
        s3://tf-datalake-bucket/deltalake-tables/unified_stac_table \
        s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_restructured \
        --date-column datetime --dry-run
"""

import argparse
import json
import logging
import math
import os
import sys
import time
from datetime import datetime, date
from pathlib import Path
from typing import Optional, List, Dict, Any

# Import parquet optimization settings
from data_marketplace.config.parquet_optimization import (
    get_optimized_stac_settings,
    get_delta_lake_properties,
    ParquetOptimizationConfig
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_storage_options(table_path: str) -> dict:
    """Setup storage options for S3 or local filesystem."""
    storage_options = {}

    if table_path.startswith("s3://"):
        # Auto-detect region from bucket
        try:
            import subprocess
            bucket = table_path.split("/")[2]
            result = subprocess.run(
                ["aws", "s3api", "get-bucket-location", "--bucket", bucket],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                import json
                location = json.loads(result.stdout).get("LocationConstraint")
                region = location if location else "us-east-1"
                storage_options["AWS_REGION"] = region
                # Also set AWS_DEFAULT_REGION for Delta Lake
                storage_options["AWS_DEFAULT_REGION"] = region
                logger.info(f"Detected S3 bucket '{bucket}' in region: {region}")
            else:
                storage_options["AWS_REGION"] = "us-west-2"  # Default
                storage_options["AWS_DEFAULT_REGION"] = "us-west-2"
                logger.warning(f"Could not detect bucket region, using default: us-west-2")
        except Exception as e:
            storage_options["AWS_REGION"] = "us-west-2"  # Default
            storage_options["AWS_DEFAULT_REGION"] = "us-west-2"
            logger.warning(f"Error detecting bucket region: {e}, using default: us-west-2")

        logger.info("Using IAM role or AWS CLI profile for S3 access")

    return storage_options


def analyze_source_table(source_path: str, storage_options: dict) -> Dict[str, Any]:
    """Analyze source table to understand schema, partitions, and data distribution."""
    try:
        import duckdb
        from deltalake import DeltaTable
        
        logger.info(f"🔍 Analyzing source table: {source_path}")
        
        # Get Delta table metadata
        dt = DeltaTable(source_path, storage_options=storage_options)
        
        # Setup DuckDB with Delta extension
        conn = duckdb.connect()
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        
        # Configure DuckDB for 7GB RAM environment
        conn.execute("SET memory_limit='5.5GB'")
        conn.execute("SET threads=2")
        conn.execute("SET preserve_insertion_order=false")
        
        # Configure S3 access if needed
        if source_path.startswith("s3://") and storage_options:
            if "AWS_ACCESS_KEY_ID" in storage_options:
                conn.execute(f"""
                    CREATE SECRET s3_secret (
                        TYPE S3,
                        KEY_ID '{storage_options["AWS_ACCESS_KEY_ID"]}',
                        SECRET '{storage_options["AWS_SECRET_ACCESS_KEY"]}',
                        REGION '{storage_options.get("AWS_REGION", "us-west-2")}'
                    )
                """)
            else:
                conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
        
        # Get basic table info
        total_rows_result = conn.execute(f"SELECT COUNT(*) FROM delta_scan('{source_path}')").fetchone()
        total_rows = int(total_rows_result[0]) if total_rows_result and total_rows_result[0] is not None else 0
        
        # Get schema info - use Delta Lake metadata to avoid SQL parsing issues with special characters
        try:
            schema_result = conn.execute(f"DESCRIBE delta_scan('{source_path}')").fetchall()
            schema = {row[0]: row[1] for row in schema_result}
        except Exception as schema_error:
            logger.warning(f"Could not get schema via DuckDB DESCRIBE (likely special characters in column names): {schema_error}")
            # Fallback to Delta Lake schema
            schema = {field.name: str(field.type) for field in dt.schema().fields}
            logger.info("Using Delta Lake schema as fallback")
        
        # Detect date/datetime columns
        date_columns = []
        for col_name, col_type in schema.items():
            if any(keyword in col_type.lower() for keyword in ['date', 'timestamp']):
                date_columns.append(col_name)
        
        # Get current partitioning info from Delta metadata
        current_partitions = dt.metadata().partition_columns if hasattr(dt.metadata(), 'partition_columns') else []
        
        # Get date range if we can identify a date column
        date_range = None
        if date_columns:
            primary_date_col = date_columns[0]  # Use first date column found
            try:
                # Properly quote column name to handle special characters
                quoted_col = f'"{primary_date_col}"' if primary_date_col else primary_date_col
                date_range_result = conn.execute(f"""
                    SELECT
                        MIN(CAST({quoted_col} AS DATE)) as min_date,
                        MAX(CAST({quoted_col} AS DATE)) as max_date,
                        COUNT(DISTINCT CAST({quoted_col} AS DATE)) as unique_dates
                    FROM delta_scan('{source_path}')
                """).fetchone()

                if date_range_result:
                    date_range = {
                        'min_date': date_range_result[0],
                        'max_date': date_range_result[1],
                        'unique_dates': date_range_result[2],
                        'primary_date_column': primary_date_col
                    }
            except Exception as e:
                logger.warning(f"Could not analyze date range: {e}")
        
        conn.close()
        
        analysis = {
            'total_rows': total_rows,
            'schema': schema,
            'date_columns': date_columns,
            'current_partitions': current_partitions,
            'date_range': date_range,
            'version': dt.version(),
            'table_uri': dt.table_uri
        }
        
        logger.info(f"📊 Analysis complete:")
        logger.info(f"   Total rows: {total_rows:,}")
        logger.info(f"   Schema columns: {len(schema)}")
        logger.info(f"   Date columns found: {date_columns}")
        logger.info(f"   Current partitions: {current_partitions}")
        if date_range:
            logger.info(f"   Date range: {date_range['min_date']} to {date_range['max_date']} ({date_range['unique_dates']} unique dates)")
        
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing source table: {e}")
        raise


def get_date_partitions(source_path: str, storage_options: dict, date_column: str) -> List[str]:
    """Get list of unique dates for processing."""
    try:
        import duckdb
        
        logger.info(f"📅 Getting date partitions from column: {date_column}")
        
        conn = duckdb.connect()
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        conn.execute("SET memory_limit='5.5GB'")
        conn.execute("SET threads=2")
        
        # Configure S3 access if needed
        if source_path.startswith("s3://") and storage_options:
            if "AWS_ACCESS_KEY_ID" in storage_options:
                conn.execute(f"""
                    CREATE SECRET s3_secret (
                        TYPE S3,
                        KEY_ID '{storage_options["AWS_ACCESS_KEY_ID"]}',
                        SECRET '{storage_options["AWS_SECRET_ACCESS_KEY"]}',
                        REGION '{storage_options.get("AWS_REGION", "us-west-2")}'
                    )
                """)
            else:
                conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
        
        # Get unique dates ordered chronologically
        # Properly quote column name to handle special characters
        quoted_col = f'"{date_column}"' if date_column else date_column
        dates_result = conn.execute(f"""
            SELECT DISTINCT CAST({quoted_col} AS DATE) as partition_date
            FROM delta_scan('{source_path}')
            WHERE {quoted_col} IS NOT NULL
            ORDER BY partition_date
        """).fetchall()
        
        conn.close()
        
        dates = [str(row[0]) for row in dates_result]
        logger.info(f"📅 Found {len(dates)} unique dates to process")
        
        return dates
        
    except Exception as e:
        logger.error(f"Error getting date partitions: {e}")
        raise


def main():
    # Get default settings from parquet optimization config
    delta_properties = get_delta_lake_properties()
    default_target_size = int(delta_properties["delta.targetFileSize"])

    parser = argparse.ArgumentParser(
        description="Restructure Delta Lake table with date partitions using optimized settings",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument("source_path", help="Source Delta Lake table path (local or s3://)")
    parser.add_argument("target_path", help="Target Delta Lake table path (local or s3://)")
    parser.add_argument("--date-column", type=str,
                       help="Date/datetime column for partitioning (auto-detected if not specified)")
    parser.add_argument("--target-file-size", type=int, default=default_target_size,
                       help=f"Target file size in bytes (default: {default_target_size // (1024*1024)}MB from config)")
    parser.add_argument("--chunk-size", type=int, default=2000,
                       help="Rows per processing chunk (default: 2000, memory-conservative)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be done without making changes")
    parser.add_argument("--resume", action="store_true",
                       help="Resume interrupted operation")
    parser.add_argument("--force", action="store_true",
                       help="Overwrite target table if it exists")
    
    args = parser.parse_args()
    
    logger.info("🚀 Delta Lake Table Restructuring")
    logger.info("=" * 60)
    logger.info(f"📍 Source: {args.source_path}")
    logger.info(f"📍 Target: {args.target_path}")
    logger.info(f"📁 Target file size: {args.target_file_size // (1024*1024)}MB")

    # Show parquet optimization settings being used
    parquet_settings = get_optimized_stac_settings()
    logger.info(f"⚙️  Parquet optimization settings:")
    logger.info(f"   Row group size: {parquet_settings['row_group_size'] // (1024*1024)}MB")
    logger.info(f"   Data page size: {parquet_settings['data_page_size'] // 1024}KB")
    logger.info(f"   Compression: {parquet_settings['compression']}")
    logger.info(f"   Dictionary columns: {len(parquet_settings['use_dictionary'])}")
    logger.info(f"   Write batch size: {parquet_settings['write_batch_size']:,}")
    
    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No changes will be made")
    
    # Setup storage options
    source_storage_options = setup_storage_options(args.source_path)
    target_storage_options = setup_storage_options(args.target_path)
    
    try:
        # Step 1: Analyze source table
        analysis = analyze_source_table(args.source_path, source_storage_options)
        
        # Step 2: Determine date column
        date_column = args.date_column
        if not date_column:
            if analysis['date_range'] and analysis['date_range']['primary_date_column']:
                date_column = analysis['date_range']['primary_date_column']
                logger.info(f"🎯 Auto-detected date column: {date_column}")
            else:
                logger.error("❌ No date column specified and none could be auto-detected")
                logger.info("Available columns:", list(analysis['schema'].keys()))
                return 1
        
        if args.dry_run:
            logger.info(f"[DRY RUN] Would restructure table using date column: {date_column}")
            logger.info(f"[DRY RUN] Would create date-level partitions")
            logger.info(f"[DRY RUN] Would optimize files to {args.target_file_size // (1024*1024)}MB")
            return 0
        
        # Step 3: Get date partitions to process
        dates = get_date_partitions(args.source_path, source_storage_options, date_column)

        if not dates:
            logger.error("❌ No dates found for partitioning")
            return 1

        # Step 4: Process each date partition
        logger.info(f"🔄 Processing {len(dates)} date partitions...")

        success_count = 0
        for i, partition_date in enumerate(dates, 1):
            logger.info(f"📅 Processing date {i}/{len(dates)}: {partition_date}")

            try:
                success = process_date_partition(
                    args.source_path, args.target_path,
                    source_storage_options, target_storage_options,
                    date_column, partition_date, args.chunk_size,
                    mode="overwrite" if i == 1 else "append"
                )

                if success:
                    success_count += 1
                    logger.info(f"✅ Completed date {partition_date} ({success_count}/{len(dates)})")
                else:
                    logger.error(f"❌ Failed to process date {partition_date}")

            except Exception as e:
                logger.error(f"❌ Error processing date {partition_date}: {e}")
                continue

        if success_count == len(dates):
            logger.info(f"🎉 Successfully processed all {success_count} date partitions!")

            # Step 5: Optimize target table for 256MB files
            logger.info("🔧 Optimizing target table for 256MB files...")
            optimize_target_table(args.target_path, target_storage_options, args.target_file_size)

            logger.info("✅ Table restructuring complete!")
        else:
            logger.warning(f"⚠️  Completed {success_count}/{len(dates)} partitions. Some dates failed.")

    except Exception as e:
        logger.error(f"❌ Restructuring failed: {e}")
        return 1

    return 0


def process_date_partition(source_path: str, target_path: str,
                          source_storage_options: dict, target_storage_options: dict,
                          date_column: str, partition_date: str, chunk_size: int,
                          mode: str = "append") -> bool:
    """Process a single date partition from source to target table."""
    try:
        import duckdb
        import pyarrow as pa
        from deltalake import write_deltalake

        logger.info(f"🔄 Processing partition: {partition_date}")

        # Setup DuckDB
        conn = duckdb.connect()
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        conn.execute("SET memory_limit='5.5GB'")
        conn.execute("SET threads=2")

        # Configure S3 access
        if source_path.startswith("s3://") and source_storage_options:
            if "AWS_ACCESS_KEY_ID" in source_storage_options:
                conn.execute(f"""
                    CREATE SECRET s3_secret (
                        TYPE S3,
                        KEY_ID '{source_storage_options["AWS_ACCESS_KEY_ID"]}',
                        SECRET '{source_storage_options["AWS_SECRET_ACCESS_KEY"]}',
                        REGION '{source_storage_options.get("AWS_REGION", "us-west-2")}'
                    )
                """)
            else:
                conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")

        # Query data for this date partition
        # Properly quote column name to handle special characters
        quoted_col = f'"{date_column}"' if date_column else date_column
        query = f"""
            SELECT *, CAST({quoted_col} AS DATE) as date
            FROM delta_scan('{source_path}')
            WHERE CAST({quoted_col} AS DATE) = '{partition_date}'
        """

        # Get total rows for this partition
        count_result = conn.execute(f"""
            SELECT COUNT(*) FROM delta_scan('{source_path}')
            WHERE CAST({quoted_col} AS DATE) = '{partition_date}'
        """).fetchone()

        total_rows = int(count_result[0]) if count_result and count_result[0] is not None else 0

        if total_rows == 0:
            logger.info(f"📅 No data found for date {partition_date}, skipping")
            conn.close()
            return True

        logger.info(f"📊 Found {total_rows:,} rows for date {partition_date}")

        # Process in chunks to manage memory
        total_chunks = math.ceil(total_rows / chunk_size)

        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size

            chunk_query = f"{query} LIMIT {chunk_size} OFFSET {offset}"

            logger.info(f"📦 Processing chunk {chunk_num + 1}/{total_chunks} (offset {offset:,})")

            # Execute query and get Arrow table
            result = conn.execute(chunk_query).arrow()

            if len(result) == 0:
                logger.info(f"📦 Chunk {chunk_num + 1} is empty, skipping")
                continue

            # Write to Delta table with optimized parquet settings
            write_mode = mode if chunk_num == 0 else "append"

            # Write to Delta table with optimized settings
            # Note: Using Delta Lake's built-in optimization instead of custom WriterProperties
            # which may not be available in all PyArrow versions
            write_deltalake(
                target_path,
                result,
                mode=write_mode,
                partition_by=["date"],
                storage_options=target_storage_options,
                # Delta Lake will use the table properties for optimization
            )

            logger.info(f"✅ Wrote chunk {chunk_num + 1}/{total_chunks} ({len(result)} rows)")

        conn.close()
        logger.info(f"✅ Completed processing date {partition_date}")
        return True

    except Exception as e:
        logger.error(f"❌ Error processing date partition {partition_date}: {e}")
        return False


def optimize_target_table(target_path: str, storage_options: dict, target_file_size: int):
    """Optimize target table to achieve target file size using optimized settings."""
    try:
        from deltalake import DeltaTable

        logger.info(f"🔧 Optimizing table for {target_file_size // (1024*1024)}MB files...")

        dt = DeltaTable(target_path, storage_options=storage_options)

        # Get Delta Lake optimization properties
        delta_properties = get_delta_lake_properties()

        # Apply Delta Lake table properties for optimization
        logger.info("📋 Applying Delta Lake optimization properties...")
        for key, value in delta_properties.items():
            logger.info(f"   {key}: {value}")

        # Run optimization with target file size and low concurrency for memory efficiency
        dt.optimize.compact(
            max_concurrent_tasks=2,  # Low concurrency to save memory (following established patterns)
            target_size=target_file_size
        )

        logger.info("✅ Table optimization complete")

        # Get final stats
        try:
            files = dt.file_uris()
            total_files = len(files)
            logger.info(f"📁 Final file count: {total_files}")
        except Exception as e:
            logger.warning(f"Could not get final file count: {e}")

    except Exception as e:
        logger.error(f"❌ Error optimizing table: {e}")
        raise


if __name__ == "__main__":
    sys.exit(main())
